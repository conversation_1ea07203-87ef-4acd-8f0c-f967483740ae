<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<?import javafx.collections.FXCollections?>
<?import java.lang.String?>

<?scenebuilder-stylesheet ../css/SettingsView.css?>

<AnchorPane xmlns="http://javafx.com/javafx/17.0.12"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="oop2.project.controller.SettingsViewController"
            prefWidth="600.0" prefHeight="400.0">
   <VBox spacing="15.0" AnchorPane.topAnchor="20.0" AnchorPane.leftAnchor="20.0"
         AnchorPane.rightAnchor="20.0" AnchorPane.bottomAnchor="20.0"
         styleClass="settings-form">

      <Label text="Application Settings" style="-fx-font-size: 18px; -fx-font-weight: bold;"/>

      <!-- Bootstrap Warning Alert -->
      <HBox fx:id="validationAlert"
            spacing="8.0"
            visible="false"
            managed="false"
            alignment="CENTER_LEFT">
         <FontIcon iconLiteral="fas-exclamation-triangle" />
         <Label fx:id="alertMessage" text="Please fix the following issues:" wrapText="true" HBox.hgrow="ALWAYS"/>
      </HBox>

      <HBox spacing="10.0">
         <Label text="Language:" minWidth="150"/>
         <ComboBox fx:id="languageComboBox" prefWidth="200">
         </ComboBox>
      </HBox>

      <HBox spacing="10.0">
         <Label text="Allow Adult Content:" minWidth="150"/>
         <CheckBox fx:id="allowAdultCheckBox"/>
      </HBox>

      <HBox spacing="10.0" alignment="CENTER_LEFT">
         <Label text="Minimum Rating:" minWidth="150"/>
         <Slider fx:id="minRatingSlider"
                min="-1"
                max="10"
                value="-1"
                showTickLabels="true"
                showTickMarks="true"
                majorTickUnit="0.5"
                minorTickCount="0"
                snapToTicks="true"
                prefWidth="200"/>
         <Label fx:id="ratingValueLabel" text="Any" minWidth="40" style="-fx-font-weight: bold; -fx-text-fill: #3498db;"/>
      </HBox>

      <HBox spacing="10.0">
         <Label text="OpenAI API Key:" minWidth="150"/>
         <TextField fx:id="openAiKeyField" promptText="Required"/>
      </HBox>

      <HBox spacing="10.0">
         <Label text="TMDB API Key:" minWidth="150"/>
         <TextField fx:id="tmdbKeyField" promptText="Required"/>
      </HBox>

      <Button fx:id="saveButton" text="Save Settings" maxWidth="150"/>
   </VBox>
</AnchorPane>
