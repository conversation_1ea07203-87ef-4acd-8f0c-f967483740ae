package oop2.project.controller;

import javafx.fxml.FXML;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.Slider;
import javafx.scene.control.TextField;
import oop2.project.config.AppConfig;
import oop2.project.model.Language;

public class MovieSearcherController {

    @FXML
    private TextField searchField;

    @FXML
    ComboBox<Language> languageFilterCombo;

    @FXML
    Slider minRatingSlider;

    @FXML
    Label ratingLabel;

    @FXML
    public void initialize() {

        var appConfig = AppConfig.getInstance();

        languageFilterCombo.getItems().addAll(Language.values());
        languageFilterCombo.setValue(Language.fromLanguageCode(appConfig.get(AppConfig.KEY_LANGUAGE)));

        // Initialize rating slider
        setupRatingSlider();
    }

    private void setupRatingSlider() {
        var appConfig = AppConfig.getInstance();

        // Set initial value from config
        double configRating = appConfig.get(AppConfig.KEY_MINIMUM_RATING, Double.class);
        minRatingSlider.setValue(configRating);

        // Update label initially
        updateRatingLabel(configRating);

        // Add listener for value changes
        minRatingSlider.valueProperty().addListener((obs, oldVal, newVal) -> updateRatingLabel(newVal.doubleValue()));
    }

    private void updateRatingLabel(double value) {
        if (value < 0) {
            ratingLabel.setText("Any");
        } else {
            ratingLabel.setText(String.format("%.1f+", value));
        }
    }

    @FXML
    private void resetSearch() {
        searchField.clear();
    }
}
