package oop2.project.controller;

import javafx.fxml.FXML;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.Slider;
import javafx.scene.control.TextField;
import oop2.project.config.AppConfig;
import oop2.project.model.Language;
import oop2.project.util.RatingSliderUtil;

public class MovieSearcherController {

    @FXML
    private TextField searchField;

    @FXML
    ComboBox<Language> languageFilterCombo;

    @FXML
    Slider minRatingSlider;

    @FXML
    Label ratingLabel;

    @FXML
    public void initialize() {

        var appConfig = AppConfig.getInstance();

        languageFilterCombo.getItems().addAll(Language.values());
        languageFilterCombo.setValue(Language.fromLanguageCode(appConfig.get(AppConfig.KEY_LANGUAGE)));

        // Setup rating slider using utility class
        RatingSliderUtil.setupRatingSlider(minRatingSlider, ratingLabel);
    }



    @FXML
    private void resetSearch() {
        searchField.clear();
    }
}
