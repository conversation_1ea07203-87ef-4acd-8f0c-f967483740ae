package oop2.project.util;

import javafx.scene.control.Label;
import javafx.scene.control.Slider;
import oop2.project.config.AppConfig;

/**
 * Utility class for setting up minimum rating sliders consistently across the application.
 * This consolidates the duplicated slider logic that was previously in both 
 * SettingsViewController and MovieSearcherController.
 */
public class RatingSliderUtil {
    
    /**
     * Sets up a rating slider with the standard configuration and binds it to a label.
     * 
     * @param slider the Slider component to configure
     * @param label the Label component to update with the rating value
     */
    public static void setupRatingSlider(Slider slider, Label label) {
        AppConfig config = AppConfig.getInstance();
        
        // Set initial value from config
        double configRating = config.get(AppConfig.KEY_MINIMUM_RATING, Double.class);
        slider.setValue(configRating);
        
        // Update label initially
        updateRatingLabel(label, configRating);
        
        // Add listener for value changes
        slider.valueProperty().addListener((obs, oldVal, newVal) -> 
            updateRatingLabel(label, newVal.doubleValue()));
    }
    
    /**
     * Updates the rating label text based on the slider value.
     * 
     * @param label the Label to update
     * @param value the rating value from the slider
     */
    public static void updateRatingLabel(Label label, double value) {
        if (value < 0) {
            label.setText("Any");
        } else {
            label.setText(String.format("%.1f+", value));
        }
    }
    
    /**
     * Refreshes a slider's value from the current AppConfig.
     *
     * @param slider the Slider to refresh
     * @param label the Label to update (optional, can be null)
     */
    public static void refreshSliderFromConfig(Slider slider, Label label) {
        AppConfig config = AppConfig.getInstance();
        double configRating = config.get(AppConfig.KEY_MINIMUM_RATING, Double.class);
        slider.setValue(configRating);
        if (label != null) {
            updateRatingLabel(label, configRating);
        }
    }

    /**
     * Gets the current value from a rating slider.
     *
     * @param slider the Slider to get the value from
     * @return the current slider value
     */
    public static double getSliderValue(Slider slider) {
        return slider.getValue();
    }
}
