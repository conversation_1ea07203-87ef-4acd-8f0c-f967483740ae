package oop2.project.controller;

import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.util.StringConverter;
import oop2.project.config.AppConfig;
import oop2.project.model.Language;
import oop2.project.util.RatingSliderUtil;

import java.util.ArrayList;
import java.util.List;

public class SettingsViewController {
    @FXML
    private ComboBox<Language> languageComboBox;

    @FXML
    private CheckBox allowAdultCheckBox;

    @FXML
    private Slider minRatingSlider;

    @FXML
    private Label ratingValueLabel;

    @FXML
    private TextField openAiKeyField;

    @FXML
    private TextField tmdbKeyField;

    @FXML
    private Button saveButton;

    @FXML
    private HBox validationAlert;

    @FXML
    private Label alertMessage;

    private final AppConfig config = AppConfig.getInstance();

    private MainViewController mainViewController;

    @FXML
    public void initialize() {
        setupComboBox();

        languageComboBox.setValue(Language.fromLanguageCode(config.get(AppConfig.KEY_LANGUAGE)));
        allowAdultCheckBox.setSelected(config.get(AppConfig.KEY_ALLOW_ADULT_CONTENT, Boolean.class));

        // Setup rating slider using utility class
        RatingSliderUtil.setupRatingSlider(minRatingSlider, ratingValueLabel);

        openAiKeyField.setText(config.get(AppConfig.KEY_OPENAI_API_KEY));
        tmdbKeyField.setText(config.get(AppConfig.KEY_TMDB_API_KEY));

        // Add validation listeners to API key fields
        openAiKeyField.textProperty().addListener((observable, oldValue, newValue) -> validateSettings());
        tmdbKeyField.textProperty().addListener((observable, oldValue, newValue) -> validateSettings());

        // Save config on button click
        saveButton.setOnAction(e -> saveSettings());

        // Initial validation
        validateSettings();
    }

    private void setupComboBox() {
        languageComboBox.getItems().addAll(Language.values());

        languageComboBox.setConverter(new StringConverter<>() {
            @Override
            public String toString(Language language) {
                return language.toString();
            }

            @Override
            public Language fromString(String string) {
                return Language.fromLanguageCode(string);
            }
        });
    }



    private void saveSettings() {
        // Validate before saving
        if (!isValidSettings()) {
            return; // Don't save if validation fails
        }

        config.set(AppConfig.KEY_LANGUAGE, languageComboBox.getValue().getLanguageCode());
        config.set(AppConfig.KEY_ALLOW_ADULT_CONTENT, Boolean.toString(allowAdultCheckBox.isSelected()));
        config.set(AppConfig.KEY_MINIMUM_RATING, String.valueOf(RatingSliderUtil.getSliderValue(minRatingSlider)));
        config.set(AppConfig.KEY_OPENAI_API_KEY, openAiKeyField.getText().trim());
        config.set(AppConfig.KEY_TMDB_API_KEY, tmdbKeyField.getText().trim());

        config.save();

        // Re-validate after saving to update the alert
        validateSettings();

        // Notify main controller that settings were updated
        if (mainViewController != null) {
            mainViewController.onSettingsUpdated();
        }
    }

    private void validateSettings() {
        List<String> errors = new ArrayList<>();

        // Check OpenAI API Key
        if (openAiKeyField.getText() == null || openAiKeyField.getText().trim().isEmpty()) {
            errors.add("OpenAI API Key is required");
        }

        // Check TMDB API Key
        if (tmdbKeyField.getText() == null || tmdbKeyField.getText().trim().isEmpty()) {
            errors.add("TMDB API Key is required");
        }

        // Show or hide alert based on validation results
        if (errors.isEmpty()) {
            hideValidationAlert();
        } else {
            showValidationAlert(errors);
        }
    }

    private boolean isValidSettings() {
        String openAiKey = openAiKeyField.getText();
        String tmdbKey = tmdbKeyField.getText();

        return openAiKey != null && !openAiKey.trim().isEmpty() &&
               tmdbKey != null && !tmdbKey.trim().isEmpty();
    }

    private void showValidationAlert(List<String> errors) {
        if (errors.size() == 1) {
            alertMessage.setText(errors.getFirst() + ".");
        } else {
            StringBuilder message = new StringBuilder("Please fix the following issues: ");
            for (int i = 0; i < errors.size(); i++) {
                if (i > 0) {
                    message.append(", ");
                }
                message.append(errors.get(i));
            }
            message.append(".");
            alertMessage.setText(message.toString());
        }

        validationAlert.setVisible(true);
        validationAlert.setManaged(true);
    }

    private void hideValidationAlert() {
        validationAlert.setVisible(false);
        validationAlert.setManaged(false);
    }

    /**
     * Set the main view controller for communication
     */
    public void setMainViewController(MainViewController mainViewController) {
        this.mainViewController = mainViewController;
    }
}
