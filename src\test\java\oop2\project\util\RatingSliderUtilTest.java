package oop2.project.util;

import javafx.scene.control.Label;
import javafx.scene.control.Slider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.testfx.framework.junit5.ApplicationExtension;
import oop2.project.config.AppConfig;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for RatingSliderUtil to verify the extracted slider logic works correctly.
 */
@ExtendWith(ApplicationExtension.class)
class RatingSliderUtilTest {

    private Slider slider;
    private Label label;
    private AppConfig config;

    @BeforeEach
    void setUp() {
        slider = new Slider(-1, 10, -1);
        slider.setMajorTickUnit(0.5);
        slider.setSnapToTicks(true);
        
        label = new Label();
        config = AppConfig.getInstance();
    }

    @Test
    void testSetupRatingSlider() {
        // Given: A fresh slider and label
        // When: Setting up the slider
        RatingSliderUtil.setupRatingSlider(slider, label);
        
        // Then: The slider should be configured with the config value
        double expectedValue = config.get(AppConfig.KEY_MINIMUM_RATING, Double.class);
        assertEquals(expectedValue, slider.getValue(), 0.01);
        
        // And the label should be updated correctly
        if (expectedValue < 0) {
            assertEquals("Any", label.getText());
        } else {
            assertEquals(String.format("%.1f+", expectedValue), label.getText());
        }
    }

    @Test
    void testUpdateRatingLabelWithNegativeValue() {
        // Given: A negative rating value
        double negativeValue = -1.0;
        
        // When: Updating the label
        RatingSliderUtil.updateRatingLabel(label, negativeValue);
        
        // Then: The label should show "Any"
        assertEquals("Any", label.getText());
    }

    @Test
    void testUpdateRatingLabelWithPositiveValue() {
        // Given: A positive rating value
        double positiveValue = 7.5;
        
        // When: Updating the label
        RatingSliderUtil.updateRatingLabel(label, positiveValue);
        
        // Then: The label should show the formatted rating
        assertEquals("7.5+", label.getText());
    }

    @Test
    void testGetSliderValue() {
        // Given: A slider with a specific value
        double testValue = 8.0;
        slider.setValue(testValue);
        
        // When: Getting the slider value through the utility
        double actualValue = RatingSliderUtil.getSliderValue(slider);
        
        // Then: The values should match
        assertEquals(testValue, actualValue, 0.01);
    }

    @Test
    void testRefreshSliderFromConfig() {
        // Given: A slider with a different value than config
        slider.setValue(5.0);
        double configValue = config.get(AppConfig.KEY_MINIMUM_RATING, Double.class);
        
        // When: Refreshing from config
        RatingSliderUtil.refreshSliderFromConfig(slider, label);
        
        // Then: The slider should have the config value
        assertEquals(configValue, slider.getValue(), 0.01);
        
        // And the label should be updated
        if (configValue < 0) {
            assertEquals("Any", label.getText());
        } else {
            assertEquals(String.format("%.1f+", configValue), label.getText());
        }
    }

    @Test
    void testSliderValueChangeUpdatesLabel() {
        // Given: A configured slider and label
        RatingSliderUtil.setupRatingSlider(slider, label);
        
        // When: Changing the slider value
        double newValue = 6.5;
        slider.setValue(newValue);
        
        // Then: The label should be updated automatically (due to the listener)
        // Note: This test verifies that the listener was properly set up
        assertEquals("6.5+", label.getText());
    }
}
