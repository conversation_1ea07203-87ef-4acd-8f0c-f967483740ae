/* Movie Card Styles */

/* Main Card Container */
.movie-card {
    -fx-background-color: white;
    -fx-background-radius: 16;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 15, 0, 0, 4);
    -fx-cursor: hand;
    -fx-transition: all 0.3s ease;
}

.movie-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 25, 0, 0, 8);
    -fx-translate-y: -4;
}

/* Poster Container */
.poster-container {
    -fx-background-radius: 16 16 0 0;
    -fx-clip: true;
    -fx-min-height: 420;
    -fx-max-height: 420;
}

.movie-poster {
    -fx-background-radius: 16 16 0 0;
    -fx-clip: true;
}

/* Poster Placeholder */
.poster-placeholder {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 16 16 0 0;
    -fx-min-height: 420;
    -fx-max-height: 420;
}

.placeholder-icon {
    -fx-icon-color: #bdc3c7;
}

.placeholder-text {
    -fx-font-size: 14px;
    -fx-text-fill: #95a5a6;
    -fx-font-weight: 500;
}

/* Rating Badge */
.rating-badge {
    -fx-background-color: rgba(0,0,0,0.8);
    -fx-background-radius: 12;
    -fx-padding: 6 10;
}

.rating-star {
    -fx-icon-color: #f39c12;
}

.rating-text {
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
}

/* Favorite Button */
.favorite-btn {
    -fx-background-color: rgba(255,255,255,0.9);
    -fx-background-radius: 20;
    -fx-border-color: transparent;
    -fx-padding: 8;
    -fx-min-width: 36;
    -fx-min-height: 36;
    -fx-cursor: hand;
}

.favorite-btn:hover {
    -fx-background-color: white;
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

.favorite-btn .ikonli-font-icon {
    -fx-icon-color: #e74c3c;
}

.favorite-btn.favorited .ikonli-font-icon {
    -fx-icon-literal: "fas-heart";
}

/* Hover Overlay */
.hover-overlay {
    -fx-background-color: rgba(0,0,0,0.7);
    -fx-background-radius: 16 16 0 0;
    -fx-opacity: 0;
    -fx-transition: opacity 0.3s ease;
}

.movie-card:hover .hover-overlay {
    -fx-opacity: 1;
}

.play-btn {
    -fx-background-color: #e74c3c;
    -fx-background-radius: 30;
    -fx-border-color: transparent;
    -fx-padding: 15;
    -fx-min-width: 60;
    -fx-min-height: 60;
    -fx-cursor: hand;
}

.play-btn:hover {
    -fx-background-color: #c0392b;
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

.play-btn .ikonli-font-icon {
    -fx-icon-color: white;
}

.action-btn {
    -fx-background-color: rgba(255,255,255,0.2);
    -fx-background-radius: 20;
    -fx-border-color: rgba(255,255,255,0.3);
    -fx-border-radius: 20;
    -fx-border-width: 1;
    -fx-padding: 8;
    -fx-min-width: 36;
    -fx-min-height: 36;
    -fx-cursor: hand;
}

.action-btn:hover {
    -fx-background-color: rgba(255,255,255,0.3);
    -fx-border-color: rgba(255,255,255,0.5);
}

.action-btn .ikonli-font-icon {
    -fx-icon-color: white;
}

/* Movie Info Section */
.movie-info {
    -fx-background-color: white;
    -fx-background-radius: 0 0 16 16;
}

.movie-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-line-spacing: 2;
}

.movie-year {
    -fx-font-size: 14px;
    -fx-text-fill: #7f8c8d;
    -fx-font-weight: 500;
}

.movie-duration {
    -fx-font-size: 14px;
    -fx-text-fill: #95a5a6;
    -fx-font-weight: 400;
}

/* Genre Container */
.genre-container {
    -fx-max-height: 40;
}

.genre-tag {
    -fx-background-color: #ecf0f1;
    -fx-background-radius: 12;
    -fx-padding: 4 8;
    -fx-font-size: 11px;
    -fx-text-fill: #34495e;
    -fx-font-weight: 500;
}

.genre-tag.action {
    -fx-background-color: #ffe5e5;
    -fx-text-fill: #e74c3c;
}

.genre-tag.comedy {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #f39c12;
}

.genre-tag.drama {
    -fx-background-color: #e8f4fd;
    -fx-text-fill: #3498db;
}

.genre-tag.thriller {
    -fx-background-color: #f0e6ff;
    -fx-text-fill: #9b59b6;
}

.genre-tag.horror {
    -fx-background-color: #ffe6e6;
    -fx-text-fill: #e74c3c;
}

.genre-tag.romance {
    -fx-background-color: #ffe6f0;
    -fx-text-fill: #e91e63;
}

.genre-tag.sci-fi {
    -fx-background-color: #e6f3ff;
    -fx-text-fill: #2196f3;
}

/* Movie Overview */
.movie-overview {
    -fx-font-size: 13px;
    -fx-text-fill: #7f8c8d;
    -fx-line-spacing: 1;
}

/* Info Labels */
.info-label {
    -fx-font-size: 12px;
    -fx-text-fill: #95a5a6;
    -fx-font-weight: 600;
    -fx-min-width: 50;
}

.info-value {
    -fx-font-size: 12px;
    -fx-text-fill: #34495e;
    -fx-font-weight: 400;
}

/* Action Buttons */
.action-buttons {
    -fx-padding: 10 0 0 0;
}

.primary-btn {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-padding: 8 16;
    -fx-background-radius: 8;
    -fx-border-color: transparent;
    -fx-cursor: hand;
}

.primary-btn:hover {
    -fx-background-color: #2980b9;
}

.primary-btn .ikonli-font-icon {
    -fx-icon-color: white;
}

.secondary-btn {
    -fx-background-color: transparent;
    -fx-text-fill: #7f8c8d;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-padding: 8 16;
    -fx-background-radius: 8;
    -fx-border-color: #bdc3c7;
    -fx-border-radius: 8;
    -fx-border-width: 1;
    -fx-cursor: hand;
}

.secondary-btn:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #95a5a6;
    -fx-text-fill: #2c3e50;
}

.secondary-btn .ikonli-font-icon {
    -fx-icon-color: #7f8c8d;
}

.secondary-btn:hover .ikonli-font-icon {
    -fx-icon-color: #2c3e50;
}

/* List View Variant */
.movie-card.list-view {
    -fx-pref-width: -1;
    -fx-max-width: -1;
    -fx-orientation: horizontal;
}

.movie-card.list-view .poster-container {
    -fx-min-width: 120;
    -fx-max-width: 120;
    -fx-min-height: 180;
    -fx-max-height: 180;
    -fx-background-radius: 8;
}

.movie-card.list-view .movie-poster {
    -fx-fit-width: 120;
    -fx-fit-height: 180;
    -fx-background-radius: 8;
}

.movie-card.list-view .movie-info {
    -fx-background-radius: 0 16 16 0;
    -fx-padding: 20;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .movie-card {
        -fx-pref-width: 240;
        -fx-max-width: 240;
    }
    
    .poster-container {
        -fx-min-height: 360;
        -fx-max-height: 360;
    }
    
    .movie-poster {
        -fx-fit-width: 240;
        -fx-fit-height: 360;
    }
}
